from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
import uuid


class User(AbstractUser):
    """自定义用户模型"""

    # 使用邮箱作为用户名
    email = models.EmailField('邮箱地址', unique=True)
    username = models.CharField('用户名', max_length=150, unique=True)

    # 用户基本信息
    nickname = models.CharField('昵称', max_length=50, blank=True)
    avatar = models.ImageField('头像', upload_to='avatars/', blank=True, null=True)
    bio = models.TextField('个人简介', max_length=500, blank=True)

    # 用户状态
    is_email_verified = models.BooleanField('邮箱已验证', default=False)
    phone = models.CharField('手机号', max_length=20, blank=True)
    is_phone_verified = models.BooleanField('手机已验证', default=False)

    # 用户设置
    receive_email_notifications = models.BooleanField('接收邮件通知', default=True)
    receive_sms_notifications = models.BooleanField('接收短信通知', default=False)

    # 统计信息
    post_count = models.PositiveIntegerField('发帖数', default=0)
    comment_count = models.PositiveIntegerField('评论数', default=0)
    like_count = models.PositiveIntegerField('获赞数', default=0)

    # 时间戳
    created_at = models.DateTimeField('注册时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    last_login_ip = models.GenericIPAddressField('最后登录IP', blank=True, null=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-created_at']

    def __str__(self):
        return self.email

    @property
    def display_name(self):
        """显示名称：优先使用昵称，其次用户名"""
        return self.nickname or self.username

    def get_avatar_url(self):
        """获取头像URL"""
        if self.avatar:
            return self.avatar.url
        return '/static/images/default-avatar.png'


class EmailVerificationCode(models.Model):
    """邮箱验证码模型"""

    CODE_TYPE_CHOICES = [
        ('register', '注册验证'),
        ('login', '登录验证'),
        ('reset_password', '重置密码'),
        ('change_email', '更换邮箱'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField('邮箱地址')
    code = models.CharField('验证码', max_length=6)
    code_type = models.CharField('验证码类型', max_length=20, choices=CODE_TYPE_CHOICES)

    # 验证状态
    is_used = models.BooleanField('已使用', default=False)
    used_at = models.DateTimeField('使用时间', blank=True, null=True)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    expires_at = models.DateTimeField('过期时间')

    # 安全相关
    ip_address = models.GenericIPAddressField('请求IP', blank=True, null=True)
    user_agent = models.TextField('用户代理', blank=True)

    class Meta:
        db_table = 'email_verification_codes'
        verbose_name = '邮箱验证码'
        verbose_name_plural = '邮箱验证码'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'code_type', 'is_used']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'{self.email} - {self.get_code_type_display()}'

    def is_expired(self):
        """检查验证码是否过期"""
        return timezone.now() > self.expires_at

    def is_valid(self):
        """检查验证码是否有效"""
        return not self.is_used and not self.is_expired()

    def mark_as_used(self):
        """标记验证码为已使用"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])


class LoginLog(models.Model):
    """登录日志模型"""

    LOGIN_TYPE_CHOICES = [
        ('password', '密码登录'),
        ('email_code', '邮箱验证码登录'),
        ('phone_code', '手机验证码登录'),
        ('social', '第三方登录'),
    ]

    STATUS_CHOICES = [
        ('success', '成功'),
        ('failed', '失败'),
        ('blocked', '被阻止'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户', null=True, blank=True)
    email = models.EmailField('登录邮箱')
    login_type = models.CharField('登录方式', max_length=20, choices=LOGIN_TYPE_CHOICES)
    status = models.CharField('登录状态', max_length=20, choices=STATUS_CHOICES)

    # 请求信息
    ip_address = models.GenericIPAddressField('IP地址')
    user_agent = models.TextField('用户代理')
    location = models.CharField('登录地点', max_length=100, blank=True)

    # 失败原因
    failure_reason = models.CharField('失败原因', max_length=200, blank=True)

    # 时间戳
    created_at = models.DateTimeField('登录时间', auto_now_add=True)

    class Meta:
        db_table = 'login_logs'
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['email', 'status']),
            models.Index(fields=['ip_address']),
        ]

    def __str__(self):
        return f'{self.email} - {self.get_status_display()} - {self.created_at}'
