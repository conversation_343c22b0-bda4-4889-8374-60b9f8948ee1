from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from drf_spectacular.utils import extend_schema, extend_schema_view
from drf_spectacular.openapi import OpenApiParameter
from django.db.models import Q

from .models import User, EmailVerificationCode, LoginLog
from .serializers import (
    UserSerializer, UserDetailSerializer, UserCreateSerializer,
    UserUpdateSerializer, ChangePasswordSerializer,
    EmailVerificationCodeSerializer, LoginLogSerializer
)


@extend_schema_view(
    list=extend_schema(
        summary="获取用户列表",
        description="获取系统中的用户列表，支持搜索和过滤",
        parameters=[
            OpenApiParameter(
                name='search',
                description='搜索用户名、邮箱或昵称',
                required=False,
                type=str
            ),
            OpenApiParameter(
                name='is_active',
                description='过滤活跃用户',
                required=False,
                type=bool
            ),
        ],
        tags=['users']
    ),
    retrieve=extend_schema(
        summary="获取用户详情",
        description="根据用户ID获取用户详细信息",
        tags=['users']
    ),
    create=extend_schema(
        summary="创建用户",
        description="注册新用户账户",
        tags=['users']
    ),
    update=extend_schema(
        summary="更新用户信息",
        description="更新用户的基本信息",
        tags=['users']
    ),
    partial_update=extend_schema(
        summary="部分更新用户信息",
        description="部分更新用户的基本信息",
        tags=['users']
    ),
    destroy=extend_schema(
        summary="删除用户",
        description="删除用户账户（软删除）",
        tags=['users']
    ),
)
class UserViewSet(ModelViewSet):
    """用户管理视图集"""

    queryset = User.objects.all()
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['username', 'email', 'nickname']
    filterset_fields = ['is_active', 'is_email_verified', 'is_phone_verified']
    ordering_fields = ['created_at', 'last_login', 'post_count', 'comment_count']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return UserSerializer
        elif self.action in ['create']:
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        else:
            return UserDetailSerializer

    def get_permissions(self):
        """根据操作设置权限"""
        if self.action == 'create':
            # 注册不需要认证
            permission_classes = [permissions.AllowAny]
        elif self.action in ['update', 'partial_update', 'destroy']:
            # 只能修改自己的信息
            permission_classes = [permissions.IsAuthenticated]
        else:
            # 其他操作需要认证
            permission_classes = [permissions.IsAuthenticated]

        return [permission() for permission in permission_classes]

    def get_object(self):
        """获取对象，支持'me'关键字获取当前用户"""
        if self.kwargs.get('pk') == 'me':
            return self.request.user
        return super().get_object()

    def perform_update(self, serializer):
        """更新时检查权限"""
        if self.get_object() != self.request.user and not self.request.user.is_staff:
            raise permissions.PermissionDenied("只能修改自己的信息")
        serializer.save()

    def perform_destroy(self, instance):
        """软删除用户"""
        if instance != self.request.user and not self.request.user.is_staff:
            raise permissions.PermissionDenied("只能删除自己的账户")
        instance.is_active = False
        instance.save()

    @extend_schema(
        summary="修改密码",
        description="修改当前用户的密码",
        request=ChangePasswordSerializer,
        responses={200: {"description": "密码修改成功"}},
        tags=['users']
    )
    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def change_password(self, request, pk=None):
        """修改密码"""
        user = self.get_object()
        if user != request.user and not request.user.is_staff:
            return Response(
                {"detail": "只能修改自己的密码"},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = ChangePasswordSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response({"detail": "密码修改成功"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(
        summary="获取用户统计信息",
        description="获取用户的发帖、评论、点赞等统计信息",
        responses={200: {
            "type": "object",
            "properties": {
                "post_count": {"type": "integer", "description": "发帖数"},
                "comment_count": {"type": "integer", "description": "评论数"},
                "like_count": {"type": "integer", "description": "获赞数"},
                "login_count": {"type": "integer", "description": "登录次数"},
                "last_login": {"type": "string", "format": "date-time", "description": "最后登录时间"}
            }
        }},
        tags=['users']
    )
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """获取用户统计信息"""
        user = self.get_object()
        login_count = LoginLog.objects.filter(user=user, status='success').count()

        return Response({
            'post_count': user.post_count,
            'comment_count': user.comment_count,
            'like_count': user.like_count,
            'login_count': login_count,
            'last_login': user.last_login
        })

    @extend_schema(
        summary="获取用户的帖子",
        description="获取指定用户发布的帖子列表",
        parameters=[
            OpenApiParameter(
                name='status',
                description='帖子状态过滤',
                required=False,
                type=str,
                enum=['draft', 'published', 'hidden', 'deleted']
            ),
        ],
        tags=['users']
    )
    @action(detail=True, methods=['get'])
    def posts(self, request, pk=None):
        """获取用户的帖子"""
        user = self.get_object()
        from apps.forum.models import Post
        from apps.forum.serializers import PostListSerializer

        posts = Post.objects.filter(author=user)

        # 状态过滤
        status_filter = request.query_params.get('status')
        if status_filter:
            posts = posts.filter(status=status_filter)

        # 如果不是本人或管理员，只显示已发布的帖子
        if user != request.user and not request.user.is_staff:
            posts = posts.filter(status='published')

        posts = posts.order_by('-created_at')

        # 分页
        page = self.paginate_queryset(posts)
        if page is not None:
            serializer = PostListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = PostListSerializer(posts, many=True, context={'request': request})
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        summary="获取邮箱验证码列表",
        description="获取邮箱验证码记录（管理员功能）",
        tags=['users']
    ),
    create=extend_schema(
        summary="发送邮箱验证码",
        description="向指定邮箱发送验证码",
        tags=['users']
    ),
)
class EmailVerificationCodeViewSet(ModelViewSet):
    """邮箱验证码管理视图集"""

    queryset = EmailVerificationCode.objects.all()
    serializer_class = EmailVerificationCodeSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['email', 'code_type', 'is_used']
    ordering = ['-created_at']

    def get_permissions(self):
        """根据操作设置权限"""
        if self.action == 'create':
            # 发送验证码允许未认证用户
            permission_classes = [permissions.AllowAny]
        else:
            # 其他操作需要管理员权限
            permission_classes = [permissions.IsAdminUser]

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """管理员可以看到所有记录，普通用户只能看到自己的"""
        if self.request.user.is_staff:
            return super().get_queryset()
        return super().get_queryset().filter(email=self.request.user.email)

    def perform_create(self, serializer):
        """创建验证码时添加请求信息"""
        request = self.context.get('request') or self.request
        ip_address = self._get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        serializer.save(
            context={
                'ip_address': ip_address,
                'user_agent': user_agent
            }
        )

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@extend_schema_view(
    list=extend_schema(
        summary="获取登录日志",
        description="获取用户登录日志记录",
        parameters=[
            OpenApiParameter(
                name='status',
                description='登录状态过滤',
                required=False,
                type=str,
                enum=['success', 'failed', 'blocked']
            ),
            OpenApiParameter(
                name='login_type',
                description='登录方式过滤',
                required=False,
                type=str,
                enum=['password', 'email_code', 'phone_code', 'social']
            ),
        ],
        tags=['users']
    ),
)
class LoginLogViewSet(ModelViewSet):
    """登录日志视图集"""

    queryset = LoginLog.objects.all()
    serializer_class = LoginLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'login_type', 'email']
    ordering = ['-created_at']
    http_method_names = ['get']  # 只允许读取操作

    def get_queryset(self):
        """用户只能看到自己的登录日志，管理员可以看到所有"""
        if self.request.user.is_staff:
            return super().get_queryset()
        return super().get_queryset().filter(user=self.request.user)
