from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import UserAccount, UserProfile, EmailVerificationCode, LoginLog


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""

    avatar_url = serializers.SerializerMethodField()
    display_name = serializers.ReadOnlyField()

    class Meta:
        model = UserProfile
        fields = [
            'username', 'nickname', 'avatar', 'avatar_url', 'bio', 'display_name',
            'phone', 'is_email_verified', 'is_phone_verified',
            'receive_email_notifications', 'receive_sms_notifications',
            'post_count', 'comment_count', 'like_count', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'is_email_verified', 'is_phone_verified',
            'post_count', 'comment_count', 'like_count', 'created_at', 'updated_at'
        ]

    def get_avatar_url(self, obj):
        """获取头像URL"""
        return obj.get_avatar_url()


class UserSerializer(serializers.ModelSerializer):
    """用户基本信息序列化器"""

    profile = UserProfileSerializer(read_only=True)
    username = serializers.CharField(source='profile.username', read_only=True)
    nickname = serializers.CharField(source='profile.nickname', read_only=True)
    avatar_url = serializers.CharField(source='profile.get_avatar_url', read_only=True)
    display_name = serializers.CharField(source='profile.display_name', read_only=True)

    class Meta:
        model = UserAccount
        fields = [
            'id', 'email', 'username', 'nickname', 'avatar_url', 'display_name',
            'is_active', 'date_joined', 'last_login', 'profile'
        ]
        read_only_fields = [
            'id', 'email', 'username', 'nickname', 'avatar_url', 'display_name',
            'is_active', 'date_joined', 'last_login', 'profile'
        ]


class UserDetailSerializer(UserSerializer):
    """用户详细信息序列化器"""

    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields  # 已经包含了profile中的所有信息


class UserCreateSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""

    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    username = serializers.CharField(write_only=True)
    nickname = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = UserAccount
        fields = ['email', 'password', 'password_confirm', 'username', 'nickname']
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("两次输入的密码不一致")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        username = validated_data.pop('username')
        nickname = validated_data.pop('nickname', '')

        # 创建用户账户
        user = UserAccount.objects.create_user(**validated_data)

        # 更新用户资料
        profile = user.profile
        profile.username = username
        profile.nickname = nickname
        profile.save()

        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器"""

    class Meta:
        model = UserProfile
        fields = [
            'username', 'nickname', 'avatar', 'bio', 'phone',
            'receive_email_notifications', 'receive_sms_notifications'
        ]

    def validate_username(self, value):
        profile = self.instance
        if UserProfile.objects.exclude(pk=profile.pk).filter(username=value).exists():
            raise serializers.ValidationError("该用户名已被使用")
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """修改密码序列化器"""
    
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(required=True)
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("原密码错误")
        return value
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("两次输入的新密码不一致")
        return attrs
    
    def save(self):
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class EmailVerificationCodeSerializer(serializers.ModelSerializer):
    """邮箱验证码序列化器"""
    
    class Meta:
        model = EmailVerificationCode
        fields = ['email', 'code_type']
        
    def create(self, validated_data):
        # 这里应该包含发送验证码的逻辑
        # 暂时返回一个简单的实现
        import random
        import string
        from datetime import timedelta
        from django.utils import timezone
        
        code = ''.join(random.choices(string.digits, k=6))
        expires_at = timezone.now() + timedelta(minutes=10)
        
        # 删除同类型的旧验证码
        EmailVerificationCode.objects.filter(
            email=validated_data['email'],
            code_type=validated_data['code_type'],
            is_used=False
        ).delete()
        
        verification_code = EmailVerificationCode.objects.create(
            email=validated_data['email'],
            code=code,
            code_type=validated_data['code_type'],
            expires_at=expires_at,
            ip_address=self.context.get('ip_address'),
            user_agent=self.context.get('user_agent', '')
        )
        
        # TODO: 实际发送邮件的逻辑
        
        return verification_code


class LoginLogSerializer(serializers.ModelSerializer):
    """登录日志序列化器"""
    
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    
    class Meta:
        model = LoginLog
        fields = [
            'id', 'user', 'user_display', 'email', 'login_type', 'status',
            'ip_address', 'location', 'failure_reason', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
