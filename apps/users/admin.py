from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, EmailVerificationCode, LoginLog


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理"""

    list_display = [
        'username', 'email', 'nickname', 'is_email_verified',
        'post_count', 'comment_count', 'like_count',
        'is_active', 'is_staff', 'created_at'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser',
        'is_email_verified', 'is_phone_verified',
        'receive_email_notifications', 'receive_sms_notifications',
        'created_at', 'last_login'
    ]
    search_fields = ['username', 'email', 'nickname', 'phone']
    readonly_fields = [
        'id', 'post_count', 'comment_count', 'like_count',
        'created_at', 'updated_at', 'last_login_ip'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('username', 'email', 'password')
        }),
        ('个人信息', {
            'fields': ('nickname', 'avatar', 'bio', 'phone')
        }),
        ('验证状态', {
            'fields': ('is_email_verified', 'is_phone_verified')
        }),
        ('通知设置', {
            'fields': ('receive_email_notifications', 'receive_sms_notifications')
        }),
        ('权限设置', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('统计信息', {
            'fields': ('post_count', 'comment_count', 'like_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'last_login', 'last_login_ip'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = (
        ('基本信息', {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2'),
        }),
        ('个人信息', {
            'fields': ('nickname', 'bio')
        }),
    )

    ordering = ['-created_at']
    filter_horizontal = ['groups', 'user_permissions']


@admin.register(EmailVerificationCode)
class EmailVerificationCodeAdmin(admin.ModelAdmin):
    """邮箱验证码管理"""

    list_display = [
        'email', 'code', 'code_type', 'is_used',
        'created_at', 'expires_at', 'is_expired_display'
    ]
    list_filter = [
        'code_type', 'is_used', 'created_at', 'expires_at'
    ]
    search_fields = ['email', 'code', 'ip_address']
    readonly_fields = [
        'id', 'created_at', 'used_at', 'is_expired_display'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('email', 'code', 'code_type')
        }),
        ('状态信息', {
            'fields': ('is_used', 'used_at', 'expires_at', 'is_expired_display')
        }),
        ('安全信息', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def is_expired_display(self, obj):
        """过期状态显示"""
        if obj.is_expired():
            return format_html('<span style="color: red;">已过期</span>')
        return format_html('<span style="color: green;">有效</span>')
    is_expired_display.short_description = '是否过期'

    ordering = ['-created_at']


@admin.register(LoginLog)
class LoginLogAdmin(admin.ModelAdmin):
    """登录日志管理"""

    list_display = [
        'email', 'user_link', 'login_type', 'status',
        'ip_address', 'location', 'created_at'
    ]
    list_filter = [
        'login_type', 'status', 'created_at'
    ]
    search_fields = [
        'email', 'user__username', 'ip_address',
        'location', 'failure_reason'
    ]
    readonly_fields = [
        'id', 'created_at', 'user_agent_display'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'email', 'login_type', 'status')
        }),
        ('请求信息', {
            'fields': ('ip_address', 'location', 'user_agent_display')
        }),
        ('失败信息', {
            'fields': ('failure_reason',)
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def user_link(self, obj):
        """用户链接"""
        if obj.user:
            from django.urls import reverse
            url = reverse('admin:users_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return '未知用户'
    user_link.short_description = '用户'

    def user_agent_display(self, obj):
        """用户代理显示"""
        if obj.user_agent:
            return obj.user_agent[:100] + '...' if len(obj.user_agent) > 100 else obj.user_agent
        return '未知'
    user_agent_display.short_description = '用户代理'

    ordering = ['-created_at']

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')
